'use client'

import { useState } from 'react'

export default function GroupInviteTest() {
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')

  const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'

  const testInviteFlow = async () => {
    setIsLoading(true)
    setMessage('')

    try {
      // First, create a test group
      const groupResponse = await fetch(`${API_URL}/api/groups`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          title: 'Test Group for Invitations',
          description: 'Testing the invitation notification system'
        }),
      })

      if (!groupResponse.ok) {
        throw new Error('Failed to create test group')
      }

      const groupData = await groupResponse.json()
      const groupId = groupData.data.group.id

      // Search for users to invite (this is a simplified test)
      const searchResponse = await fetch(`${API_URL}/api/users/search?q=test`, {
        credentials: 'include',
      })

      if (!searchResponse.ok) {
        throw new Error('Failed to search for users')
      }

      const searchData = await searchResponse.json()
      const users = searchData.data.users || []

      if (users.length === 0) {
        setMessage('No users found to invite. Create some test users first.')
        return
      }

      // Invite the first user found
      const userToInvite = users[0]
      const inviteResponse = await fetch(`${API_URL}/api/groups/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          group_id: groupId,
          user_ids: [userToInvite.id]
        }),
      })

      if (!inviteResponse.ok) {
        throw new Error('Failed to send invitation')
      }

      setMessage(`Successfully created group "${groupData.data.group.title}" and invited ${userToInvite.first_name} ${userToInvite.last_name}. Check notifications!`)

    } catch (err) {
      console.error('Test error:', err)
      setMessage(`Error: ${err.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px', borderRadius: '8px' }}>
      <h3>Group Invitation Test</h3>
      <p>This will create a test group and send an invitation to test the notification system.</p>
      
      <button 
        onClick={testInviteFlow}
        disabled={isLoading}
        style={{
          padding: '10px 20px',
          backgroundColor: isLoading ? '#ccc' : '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: isLoading ? 'not-allowed' : 'pointer'
        }}
      >
        {isLoading ? 'Testing...' : 'Test Group Invitation Flow'}
      </button>

      {message && (
        <div style={{ 
          marginTop: '15px', 
          padding: '10px', 
          backgroundColor: message.includes('Error') ? '#f8d7da' : '#d4edda',
          color: message.includes('Error') ? '#721c24' : '#155724',
          borderRadius: '4px'
        }}>
          {message}
        </div>
      )}
    </div>
  )
}
