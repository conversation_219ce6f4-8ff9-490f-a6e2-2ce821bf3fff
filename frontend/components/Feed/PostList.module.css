.postList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem 0;
}

.postCard {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1rem;
}

.postHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.userDetails {
  display: flex;
  flex-direction: column;
}

.userName {
  font-weight: 600;
  color: #1a1a1a;
}

.postTime {
  font-size: 0.875rem;
  color: #666;
}

.postPrivacy {
  color: #666;
}

.postContent {
  margin-bottom: 1rem;
  white-space: pre-wrap;
  word-break: break-word;
}

.postImage {
  margin-bottom: 1rem;
  border-radius: 8px;
  overflow: hidden;
}

.postImage img {
  width: 100%;
  height: auto;
  display: block;
}

.postActions {
  display: flex;
  gap: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #eee;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: none;
  background: none;
  color: #666;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.actionButton:hover {
  background-color: #f5f5f5;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.error {
  text-align: center;
  padding: 2rem;
  color: #dc3545;
}

.noPosts {
  text-align: center;
  padding: 2rem;
  color: #666;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
} 