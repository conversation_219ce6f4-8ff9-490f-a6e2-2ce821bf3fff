.profileHeader {
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow);
  overflow: hidden;
  margin-bottom: 20px;
}

.profileCover {
  height: 150px;
  background-color: var(--primary-navy);
  position: relative;
}

.profileCover:hover .coverEditOverlay {
  opacity: 1;
}

.coverImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.coverPlaceholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-navy) 0%, var(--secondary-navy) 100%);
}

.coverEditOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.coverEditButton {
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--text-dark);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s;
}

.coverEditButton:hover {
  background-color: white;
  transform: scale(1.1);
}

.profileAvatar {
  position: absolute;
  bottom: -50px;
  left: 30px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 4px solid var(--white);
  overflow: hidden;
  background-color: var(--light-gray);
}

.profileAvatar:hover .avatarEditButton {
  opacity: 1;
}

.profileAvatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatarPlaceholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--secondary-navy);
  color: var(--white);
  font-size: 36px;
  font-weight: bold;
}

.avatarEditButton {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--primary-navy);
  color: white;
  border: 2px solid white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  opacity: 0;
  transition: all 0.3s;
}

.avatarEditButton:hover {
  background-color: var(--secondary-navy);
  transform: scale(1.1);
}

.profileInfo {
  padding: 60px 30px 30px;
}

.profileNameRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.profileName {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-dark);
}

.nickname {
  font-weight: normal;
  color: var(--text-light);
  margin-left: 8px;
  font-size: 18px;
}

.followButton {
  background-color: var(--primary-navy);
  color: var(--white);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s;
}

.followButton:hover {
  background-color: var(--secondary-navy);
}

.followButton.following {
  background-color: var(--light-gray);
  color: var(--text-dark);
}

.buttonLoader {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--white);
  animation: spin 1s linear infinite;
}

.profileActions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.editButton {
  background-color: var(--primary-navy);
  color: var(--white);
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s;
  font-size: 14px;
}

.editButton:hover {
  background-color: var(--secondary-navy);
}

.privacyToggle {
  display: flex;
  align-items: center;
}

.toggleLabel {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
}

.toggleCheckbox {
  height: 0;
  width: 0;
  visibility: hidden;
  position: absolute;
}

.toggleSwitch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  background-color: var(--border-gray);
  border-radius: 24px;
  margin-left: 10px;
  transition: 0.3s;
}

.toggleSwitch:after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background-color: var(--white);
  border-radius: 50%;
  transition: 0.3s;
}

.toggleCheckbox:checked + .toggleSwitch {
  background-color: var(--success-green);
}

.toggleCheckbox:checked + .toggleSwitch:after {
  left: calc(100% - 2px);
  transform: translateX(-100%);
}

.profileStats {
  display: flex;
  margin-bottom: 20px;
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30px;
}

.statValue {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-dark);
}

.statLabel {
  font-size: 14px;
  color: var(--text-light);
}

.profileBio {
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--light-gray);
  border-radius: 8px;
}

.profileMeta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.metaItem {
  display: flex;
  align-items: center;
  color: var(--text-light);
  font-size: 14px;
}

.metaItem i {
  margin-right: 8px;
  width: 16px;
}

@media (max-width: 768px) {
  .profileNameRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .profileActions {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .editButton {
    justify-content: center;
  }
  
  .profileAvatar {
    left: 50%;
    transform: translateX(-50%);
  }
  
  .profileInfo {
    padding-top: 70px;
    text-align: center;
  }
  
  .profileStats {
    justify-content: center;
  }
  
  .profileMeta {
    justify-content: center;
  }
}