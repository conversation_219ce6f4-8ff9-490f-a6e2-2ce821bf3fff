.registerForm {
  padding: 30px 20px;
  max-width: 600px;
  margin: 0 auto;
}

.formTitle {
  text-align: center;
  margin-bottom: 24px;
  color: var(--primary-navy);
  font-size: 24px;
  font-weight: 700;
}

.formGroup {
  margin-bottom: 20px;
}

.formLabel {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
}

.required {
  color: var(--error-red);
}

.optional {
  color: var(--text-light);
  font-weight: normal;
  font-size: 14px;
}

.formInput, .formTextarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.formInput:focus, .formTextarea:focus {
  border-color: var(--primary-navy);
  outline: none;
}

.inputError {
  border-color: var(--error-red);
}

.errorText {
  color: var(--error-red);
  font-size: 14px;
  margin-top: 4px;
}

.errorMessage {
  background-color: rgba(255, 76, 76, 0.1);
  border: 1px solid var(--error-red);
  color: var(--error-red);
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
}

.nameFields {
  display: flex;
  gap: 16px;
}

.nameFields .formGroup {
  flex: 1;
}

.passwordStrength {
  margin-top: 8px;
  display: flex;
  align-items: center;
}

.strengthBar {
  flex: 1;
  height: 6px;
  background-color: var(--border-gray);
  border-radius: 3px;
  overflow: hidden;
  margin-right: 10px;
}

.strengthFill {
  height: 100%;
  transition: width 0.3s, background-color 0.3s;
}

.strengthLabel {
  font-size: 14px;
  color: var(--text-light);
  min-width: 70px;
}

.passwordHint {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 4px;
}

.avatarUpload {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.avatarPreview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--background-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 2px dashed var(--border-gray);
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatarPlaceholder {
  font-size: 12px;
  color: var(--text-light);
  text-align: center;
  padding: 10px;
}

.fileInput {
  display: none;
}

.charCounter {
  font-size: 12px;
  color: var(--text-light);
  text-align: right;
  margin-top: 4px;
}

.btnRegister {
  width: 100%;
  background-color: var(--primary-navy);
  color: var(--white);
  border: none;
  border-radius: 8px;
  padding: 14px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 10px;
  margin-bottom: 20px;
}

.btnRegister:hover {
  background-color: var(--secondary-navy);
}

.btnRegister:disabled {
  background-color: var(--border-gray);
  cursor: not-allowed;
}

.registerFooter {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid var(--border-gray);
  color: var(--text-light);
  font-size: 14px;
}

.registerLink {
  color: var(--primary-navy);
  text-decoration: none;
  font-weight: 500;
}

.registerLink:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .nameFields {
    flex-direction: column;
    gap: 0;
  }
  
  .registerForm {
    padding: 20px 15px;
  }
}