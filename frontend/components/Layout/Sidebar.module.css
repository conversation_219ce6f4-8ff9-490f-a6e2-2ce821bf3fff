.sidebar {
  width: 250px;
  background-color: var(--white);
  box-shadow: var(--shadow);
  padding: 20px 0;
  height: calc(100vh - 60px);
  position: fixed;
  left: 0;
  overflow-y: auto;
}

.sidebarSection {
  margin-bottom: 30px;
}

.sidebarTitle {
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  color: var(--text-light);
  margin-bottom: 10px;
  padding: 0 20px;
}

.sidebarItem {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  margin: auto;
  color: var(--primary-navy);
  text-decoration: none;
  border-radius: 8px;
  transition: background-color 0.3s;
  font-weight: 400;
  cursor: pointer;
  border: none;
  background: none;
  width: 80%;
  /* text-align: left; */
  font-size: 12px;
}

.sidebarItem:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.sidebarItem:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.sidebarItem i {
  margin-right: 12px;
  width: 20px;
  font-size: 16px;
  /* text-align: center; */
}

.sidebarItem.active {
  background-color: rgba(0, 0, 0, 0.1);
}

.badge {
  margin-left: auto;
  background-color: var(--danger);
  color: var(--white);
  border-radius: 8px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s;
  }

  .sidebar.mobileOpen {
    transform: translateX(0);
  }
}
