'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useAuth } from '../../contexts/AuthContext'
import NotificationPanel from '../Notifications/NotificationPanel'
import styles from './Navbar.module.css'

export default function Navbar() {
  const [searchQuery, setSearchQuery] = useState('')
  const [showNotifications, setShowNotifications] = useState(false)
  const [unreadCount, setUnreadCount] = useState(0)
  const router = useRouter()
  const { logout } = useAuth()

  const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'

  // Fetch unread notification count
  const fetchUnreadCount = async () => {
    try {
      const response = await fetch(`${API_URL}/api/notifications?limit=1`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        setUnreadCount(data.data.unread_count || 0)
      }
    } catch (err) {
      console.error('Error fetching unread count:', err)
    }
  }

  const handleSearch = (e) => {
    e.preventDefault()
    // Handle search functionality
    console.log('Searching for:', searchQuery)
  }

  const handleLogout = async () => {
    await logout()
  }

  const handleNotificationClose = () => {
    setShowNotifications(false)
    // Refresh unread count when closing
    fetchUnreadCount()
  }

  // Fetch unread count on component mount
  useEffect(() => {
    fetchUnreadCount()

    // Set up interval to periodically check for new notifications
    const interval = setInterval(fetchUnreadCount, 30000) // Check every 30 seconds

    return () => clearInterval(interval)
  }, [])

  return (
    <nav className={styles.navbar}>
      <div className={styles.navContainer}>
        <Link href="/feed" className={styles.navBrand}>
          Ripple
        </Link>
        
        <form className={styles.navSearch} onSubmit={handleSearch}>
          <input 
            type="text" 
            placeholder="Search Ripple..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <i className="fas fa-search"></i>
        </form>
        
        <div className={styles.navActions}>
          <Link href="/feed" className={styles.navIcon}>
            <i className="fas fa-home"></i>
          </Link>
          <Link href="/groups" className={styles.navIcon}>
            <i className="fas fa-users"></i>
          </Link>
          <Link href="/chat" className={styles.navIcon}>
            <i className="fas fa-comments"></i>
            <span className="notification-badge">3</span>
          </Link>
          <div
            className={styles.navIcon}
            onClick={() => setShowNotifications(!showNotifications)}
          >
            <i className="fas fa-bell"></i>
            {unreadCount > 0 && (
              <span className="notification-badge">{unreadCount}</span>
            )}
          </div>
          <div className={styles.profileAvatar} onClick={handleLogout}>
            <i className="fas fa-user"></i>
          </div>
        </div>
      </div>
      

      <NotificationPanel
        isOpen={showNotifications}
        onClose={handleNotificationClose}
      />
    </nav>
  )
}
