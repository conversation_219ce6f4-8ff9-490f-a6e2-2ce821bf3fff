'use client'

import { useState, useEffect } from 'react'
import styles from './NotificationPanel.module.css'

export default function NotificationPanel({ isOpen, onClose }) {
  const [notifications, setNotifications] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [unreadCount, setUnreadCount] = useState(0)
  const [processingIds, setProcessingIds] = useState(new Set())

  const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'

  // Fetch notifications
  const fetchNotifications = async () => {
    try {
      const response = await fetch(`${API_URL}/api/notifications`, {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Failed to fetch notifications')
      }

      const data = await response.json()
      setNotifications(data.data.notifications || [])
      setUnreadCount(data.data.unread_count || 0)
    } catch (err) {
      console.error('Error fetching notifications:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle group invitation response
  const handleGroupInvitation = async (notificationId, membershipId, action) => {
    setProcessingIds(prev => new Set([...prev, notificationId]))

    try {
      // First, handle the group invitation
      const response = await fetch(`${API_URL}/api/groups/handle`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          membership_id: membershipId,
          action: action
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} invitation`)
      }

      // Mark notification as read
      await fetch(`${API_URL}/api/notifications/read/${notificationId}`, {
        method: 'PUT',
        credentials: 'include',
      })

      // Refresh notifications
      await fetchNotifications()
      
    } catch (err) {
      console.error(`Error ${action}ing invitation:`, err)
      alert(`Failed to ${action} invitation. Please try again.`)
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(notificationId)
        return newSet
      })
    }
  }

  // Mark notification as read
  const markAsRead = async (notificationId) => {
    try {
      await fetch(`${API_URL}/api/notifications/read/${notificationId}`, {
        method: 'PUT',
        credentials: 'include',
      })
      
      // Update local state
      setNotifications(prev => 
        prev.map(notif => 
          notif.id === notificationId 
            ? { ...notif, is_read: true }
            : notif
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (err) {
      console.error('Error marking notification as read:', err)
    }
  }

  // Format time ago
  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now - date) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  // Render notification content based on type
  const renderNotificationContent = (notification) => {
    if (notification.type === 'group_invitation') {
      return (
        <div className={styles.groupInvitationContent}>
          <div className={styles.notificationText}>
            <strong>{notification.title}</strong>
            <p>{notification.message}</p>
          </div>
          <div className={styles.invitationActions}>
            <button
              className={`${styles.acceptBtn} btn-primary`}
              onClick={() => handleGroupInvitation(notification.id, notification.related_id, 'accept')}
              disabled={processingIds.has(notification.id)}
            >
              {processingIds.has(notification.id) ? (
                <i className="fas fa-spinner fa-spin"></i>
              ) : (
                <>
                  <i className="fas fa-check"></i>
                  Accept
                </>
              )}
            </button>
            <button
              className={`${styles.declineBtn} btn-outline`}
              onClick={() => handleGroupInvitation(notification.id, notification.related_id, 'decline')}
              disabled={processingIds.has(notification.id)}
            >
              {processingIds.has(notification.id) ? (
                <i className="fas fa-spinner fa-spin"></i>
              ) : (
                <>
                  <i className="fas fa-times"></i>
                  Decline
                </>
              )}
            </button>
          </div>
        </div>
      )
    }

    // Default notification content
    return (
      <div className={styles.defaultContent}>
        <div className={styles.notificationText}>
          <strong>{notification.title}</strong>
          <p>{notification.message}</p>
        </div>
      </div>
    )
  }

  useEffect(() => {
    if (isOpen) {
      fetchNotifications()
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className={styles.overlay} onClick={onClose}>
      <div className={styles.panel} onClick={(e) => e.stopPropagation()}>
        <div className={styles.header}>
          <h3>
            <i className="fas fa-bell"></i>
            Notifications
            {unreadCount > 0 && (
              <span className={styles.unreadBadge}>{unreadCount}</span>
            )}
          </h3>
          <button className={styles.closeBtn} onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className={styles.content}>
          {isLoading ? (
            <div className={styles.loading}>
              <i className="fas fa-spinner fa-spin"></i>
              <span>Loading notifications...</span>
            </div>
          ) : notifications.length === 0 ? (
            <div className={styles.empty}>
              <i className="fas fa-bell-slash"></i>
              <p>No notifications yet</p>
            </div>
          ) : (
            <div className={styles.notificationsList}>
              {notifications.map(notification => (
                <div 
                  key={notification.id} 
                  className={`${styles.notificationItem} ${!notification.is_read ? styles.unread : ''}`}
                  onClick={() => !notification.is_read && markAsRead(notification.id)}
                >
                  {renderNotificationContent(notification)}
                  <div className={styles.timestamp}>
                    {formatTimeAgo(notification.created_at)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
