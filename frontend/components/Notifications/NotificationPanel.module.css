.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding: 60px 20px 20px 20px;
}

.panel {
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-heavy);
  width: 380px;
  max-height: 500px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  padding: 20px;
  border-bottom: 1px solid var(--border-gray);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--light-gray);
}

.header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-navy);
  display: flex;
  align-items: center;
  gap: 8px;
}

.unreadBadge {
  background-color: var(--danger);
  color: var(--white);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.closeBtn {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--text-light);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.closeBtn:hover {
  background-color: var(--border-gray);
  color: var(--primary-navy);
}

.content {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

.loading, .empty {
  padding: 40px 20px;
  text-align: center;
  color: var(--text-light);
}

.loading i, .empty i {
  font-size: 24px;
  margin-bottom: 12px;
  display: block;
}

.loading span, .empty p {
  font-size: 14px;
  margin: 0;
}

.notificationsList {
  padding: 0;
}

.notificationItem {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-gray);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.notificationItem:hover {
  background-color: var(--light-gray);
}

.notificationItem:last-child {
  border-bottom: none;
}

.notificationItem.unread {
  background-color: #f8f9ff;
  border-left: 4px solid var(--primary-navy);
}

.notificationItem.unread::before {
  content: '';
  position: absolute;
  top: 20px;
  right: 20px;
  width: 8px;
  height: 8px;
  background-color: var(--primary-blue);
  border-radius: 50%;
}

.defaultContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.groupInvitationContent {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notificationText {
  flex: 1;
}

.notificationText strong {
  font-size: 14px;
  color: var(--primary-navy);
  display: block;
  margin-bottom: 4px;
}

.notificationText p {
  font-size: 13px;
  color: var(--text-gray);
  margin: 0;
  line-height: 1.4;
}

.invitationActions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.acceptBtn, .declineBtn {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
  min-width: 70px;
  justify-content: center;
}

.acceptBtn {
  background-color: var(--success);
  color: var(--white);
}

.acceptBtn:hover:not(:disabled) {
  background-color: #28a745;
}

.declineBtn {
  background-color: transparent;
  color: var(--text-gray);
  border: 1px solid var(--border-gray);
}

.declineBtn:hover:not(:disabled) {
  background-color: var(--light-gray);
  color: var(--primary-navy);
}

.acceptBtn:disabled, .declineBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.timestamp {
  font-size: 11px;
  color: var(--text-light);
  margin-top: 8px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .overlay {
    padding: 60px 10px 10px 10px;
  }
  
  .panel {
    width: 100%;
    max-width: 350px;
  }
  
  .header {
    padding: 16px;
  }
  
  .notificationItem {
    padding: 14px 16px;
  }
  
  .invitationActions {
    flex-direction: column;
    gap: 6px;
  }
  
  .acceptBtn, .declineBtn {
    width: 100%;
  }
}

/* Scrollbar styling */
.content::-webkit-scrollbar {
  width: 6px;
}

.content::-webkit-scrollbar-track {
  background: var(--light-gray);
}

.content::-webkit-scrollbar-thumb {
  background: var(--border-gray);
  border-radius: 3px;
}

.content::-webkit-scrollbar-thumb:hover {
  background: var(--text-light);
}
