-- Debug script to check notification and membership data

-- 1. Check all group invitation notifications
SELECT 
    n.id as notification_id,
    n.user_id as notified_user,
    n.type,
    n.title,
    n.message,
    n.related_id,
    n.related_type,
    n.is_read,
    n.created_at
FROM notifications n 
WHERE n.type = 'group_invitation' 
ORDER BY n.created_at DESC;

-- 2. Check group memberships with pending status
SELECT 
    gm.id as membership_id,
    gm.group_id,
    gm.user_id,
    gm.status,
    gm.invited_by,
    gm.joined_at,
    g.title as group_title,
    u.first_name || ' ' || u.last_name as user_name
FROM group_members gm
JOIN groups g ON gm.group_id = g.id
JOIN users u ON gm.user_id = u.id
WHERE gm.status = 'pending'
ORDER BY gm.created_at DESC;

-- 3. Check if notification related_id matches membership id
SELECT 
    n.id as notification_id,
    n.user_id as notified_user,
    n.related_id as notification_related_id,
    gm.id as membership_id,
    gm.user_id as membership_user,
    gm.group_id,
    gm.status,
    gm.invited_by,
    CASE 
        WHEN n.related_id = gm.id THEN 'MATCH'
        ELSE 'MISMATCH'
    END as id_match,
    CASE 
        WHEN n.user_id = gm.user_id THEN 'MATCH'
        ELSE 'MISMATCH'
    END as user_match
FROM notifications n
LEFT JOIN group_members gm ON n.related_id = gm.id
WHERE n.type = 'group_invitation'
ORDER BY n.created_at DESC;

-- 4. Clean up any invalid notifications (optional - uncomment if needed)
-- DELETE FROM notifications 
-- WHERE type = 'group_invitation' 
-- AND (related_id IS NULL OR related_type != 'membership');

-- 5. Check users table for reference
SELECT id, first_name, last_name, email FROM users ORDER BY created_at DESC LIMIT 10;
