-- backend/pkg/db/migrations/sqlite/000008_create_group_members_table.up.sql
CREATE TABLE group_members (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    group_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    invited_by <PERSON><PERSON><PERSON><PERSON>,
    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREI<PERSON><PERSON> KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (invited_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE(group_id, user_id)
);