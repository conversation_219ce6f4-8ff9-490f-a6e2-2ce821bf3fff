# Testing Group Invitation Flow

## Steps to test:

1. **Start the backend server**:
   ```bash
   cd backend
   go run .
   ```

2. **Start the frontend**:
   ```bash
   cd frontend
   npm run dev
   ```

3. **Create test scenario**:
   - Register/login as User A
   - Create a group
   - Register/login as User B  
   - User A invites User B to the group
   - User B should see notification
   - User B tries to accept invitation

## Expected debugging output:

When User B clicks "Accept" on the invitation, we should see:

**Frontend console:**
- `Notifications data: {...}` - showing the notification structure
- `Rendering notification: {...}` - showing individual notification details
- `Handling group invitation: { notificationId: X, membershipId: Y, action: 'accept' }`
- `Request body: { membership_id: Y, action: 'accept' }`

**Backend console:**
- `HandleMembershipRequest: membershipID=Y, userID=B, action=accept`
- `HandleMembershipRequest: membershipID=Y, userID=B, action=accept` (from repository)
- `Membership details: groupID=Z, memberUserID=B, invitedBy=A, status=pending`
- `This is an invitation. Invited user: B, Current user: B`

## Potential issues to check:

1. **Null membership ID**: If `related_id` is null in notification
2. **Wrong user permissions**: If the current user is not the invited user
3. **Invalid membership status**: If the membership is not in pending status
4. **Database inconsistency**: If the membership record doesn't exist

## Quick database check:

If needed, check the database directly:

```sql
-- Check notifications
SELECT * FROM notifications WHERE type = 'group_invitation' ORDER BY created_at DESC LIMIT 5;

-- Check group memberships
SELECT * FROM group_members WHERE status = 'pending' ORDER BY created_at DESC LIMIT 5;

-- Check if notification related_id matches membership id
SELECT n.id as notification_id, n.related_id, gm.id as membership_id, gm.user_id, gm.group_id, gm.status
FROM notifications n
LEFT JOIN group_members gm ON n.related_id = gm.id
WHERE n.type = 'group_invitation'
ORDER BY n.created_at DESC LIMIT 5;
```
