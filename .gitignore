# Node.js / Next.js
node_modules/
.next/
out/
.cache/
.env.local
.env.development.local
.env.test.local
.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
/vendor/
/bin/
/pkg/
/tmp/
*.txt

# Go workspace file
go.work

# Database
*.db
*.sqlite
*.sqlite3
/data/

# Docker
.dockerignore

# IDE / Editors
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Build artifacts
/dist/
/build/

# Pictures
*.jpg
*.png
*.jpeg
*.gif
*.webp

deviceInfo.json